/**
 * Data Migration Script for Blog Tables
 *
 * This script migrates data from the blog's Supabase instance to the main Klus Supabase instance.
 * Run this script after creating the tables using the SQL migration files.
 *
 * Prerequisites:
 * 1. Install @supabase/supabase-js: npm install @supabase/supabase-js
 * 2. Set environment variables for both Supabase instances
 * 3. Ensure the target tables exist in the main Supabase instance
 */

import { createClient } from "@supabase/supabase-js";

// Source (Blog) Supabase configuration
const SOURCE_SUPABASE_URL = process.env.BLOG_SUPABASE_URL;
const SOURCE_SUPABASE_KEY = process.env.BLOG_SUPABASE_ANON_KEY;

// Target (Main Klus) Supabase configuration
const TARGET_SUPABASE_URL = process.env.KLUS_SUPABASE_URL;
const TARGET_SUPABASE_KEY = process.env.KLUS_SUPABASE_SERVICE_KEY; // Use service key for admin operations

const sourceSupabase = createClient(SOURCE_SUPABASE_URL, SOURCE_SUPABASE_KEY);
const targetSupabase = createClient(TARGET_SUPABASE_URL, TARGET_SUPABASE_KEY);

async function migrateTable(sourceTableName, targetTableName, orderBy = "id") {
  console.log(
    `Starting migration from ${sourceTableName} to ${targetTableName}`
  );

  try {
    // Fetch all data from source table
    const { data: sourceData, error: fetchError } = await sourceSupabase
      .from(sourceTableName)
      .select("*")
      .order(orderBy);

    if (fetchError) {
      throw new Error(
        `Error fetching from ${sourceTableName}: ${fetchError.message}`
      );
    }

    if (!sourceData || sourceData.length === 0) {
      console.log(`No data found in ${sourceTableName}`);
      return;
    }

    console.log(`Found ${sourceData.length} records in ${sourceTableName}`);

    // Insert data into target table in batches
    const batchSize = 100;
    for (let i = 0; i < sourceData.length; i += batchSize) {
      const batch = sourceData.slice(i, i + batchSize);

      const { error: insertError } = await targetSupabase
        .from(targetTableName)
        .insert(batch);

      if (insertError) {
        console.error(
          `Error inserting batch ${i / batchSize + 1} for ${targetTableName}:`,
          insertError
        );
        // Continue with next batch instead of stopping
      } else {
        console.log(
          `Inserted batch ${i / batchSize + 1}/${Math.ceil(
            sourceData.length / batchSize
          )} for ${targetTableName}`
        );
      }
    }

    console.log(
      `Completed migration from ${sourceTableName} to ${targetTableName}`
    );
  } catch (error) {
    console.error(
      `Migration failed for ${sourceTableName} to ${targetTableName}:`,
      error.message
    );
  }
}

async function runMigration() {
  console.log("Starting blog data migration...");

  // Migrate tables in order (authors first due to foreign key constraints)
  const tableMappings = [
    { source: "68657d51af3489125b35b691_authors", target: "authors" },
    { source: "68657d51af3489125b35b691_posts", target: "posts" },
    {
      source: "68657d51af3489125b35b691_subscribers",
      target: "blog_subscribers",
    },
    {
      source: "68657d51af3489125b35b691_content_generator_submissions",
      target: "blog_content_submissions",
    },
  ];

  for (const mapping of tableMappings) {
    await migrateTable(mapping.source, mapping.target);
  }

  console.log("Blog data migration completed!");
}

// Run the migration
runMigration().catch(console.error);
