-- Migration: Create blog subscribers table
-- Description: Creates the blog subscribers table migrated from the separate blog project

-- Create the subscribers table
CREATE TABLE IF NOT EXISTS "blog_subscribers" (
  id SERIAL PRIMARY KEY,
  name TEXT,
  email TEXT NOT NULL,
  company TEXT,
  message TEXT,
  email_sent B<PERSON><PERSON><PERSON><PERSON> DEFAULT false,
  email_sent_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create unique index on email
CREATE UNIQUE INDEX IF NOT EXISTS "idx_blog_subscribers_email" ON "blog_subscribers" (email);

-- Create index on email_sent for filtering
CREATE INDEX IF NOT EXISTS "idx_blog_subscribers_email_sent" ON "blog_subscribers" (email_sent);

-- Enable row-level security
ALTER TABLE "blog_subscribers" ENABLE ROW LEVEL SECURITY;

-- Create policies
-- Only authenticated users with admin role can view/manage subscribers
CREATE POLICY "Admins can manage subscribers" ON "blog_subscribers"
FOR ALL TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid() AND profiles.user_type = 'admin'
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid() AND profiles.user_type = 'admin'
  )
);

-- Allow anonymous users to insert new subscriptions (for newsletter signup)
CREATE POLICY "Anyone can subscribe" ON "blog_subscribers"
FOR INSERT TO anon, authenticated
WITH CHECK (true);
