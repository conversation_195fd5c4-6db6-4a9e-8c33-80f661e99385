import { createContext, useEffect, useState } from "react";
import { Loader2 } from "lucide-react";
import { atom } from "jotai";
import { useNavigate, useLocation } from "react-router-dom";

import { Session } from "@supabase/supabase-js";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Database } from "@/types/supabase";
import {
  isPublicRoute,
  ROUTE_PATHS,
  NAVIGATION_CONFIG,
  TOAST_MESSAGES,
  ADMIN_DOMAIN,
  RESTRICTED_PUBLIC_ROUTES,
} from "@/config/routes";

export const isRegisteringAtom = atom(false);

type UserProfile = Database["public"]["Tables"]["profiles"]["Row"];

export const SessionContext = createContext<{
  session: Session | null;
  userProfile: UserProfile;
  emptyUserProfile: () => void;
  initializeSession: () => Promise<void>;
}>(null);

interface SessionProviderProps {
  children: (session: Session | null) => React.ReactNode;
}

export const SessionProvider = ({ children }: SessionProviderProps) => {
  const [session, setSession] = useState<Session | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile>();
  const [loading, setLoading] = useState(true);

  const { toast } = useToast();
  const navigate = useNavigate();
  const location = useLocation();

  const currentIsPublicRoute = isPublicRoute(location.pathname);

  const handleSessionError = () => {
    setSession(null);
    localStorage.clear();
    if (location.pathname !== ROUTE_PATHS.AUTH) {
      navigate(ROUTE_PATHS.AUTH);
      toast({
        variant: "destructive",
        ...TOAST_MESSAGES.SESSION_EXPIRED,
      });
    }
  };

  const initializeSession = async () => {
    try {
      const {
        data: { session: initialSession },
        error: sessionError,
      } = await supabase.auth.getSession();

      if (sessionError) {
        console.error("Error getting initial session:", sessionError);
        handleSessionError();
        return;
      }

      if (!initialSession) {
        if (!currentIsPublicRoute) {
          const returnUrl = encodeURIComponent(
            location.pathname + location.search
          );
          navigate(
            `${ROUTE_PATHS.AUTH}?${NAVIGATION_CONFIG.RETURN_URL_PARAM}=${returnUrl}`,
            { replace: true }
          );
          return;
        }
      } else {
        setSession(initialSession);
        if (location.pathname === ROUTE_PATHS.AUTH) {
          const params = new URLSearchParams(location.search);
          const returnUrl = params.get(NAVIGATION_CONFIG.RETURN_URL_PARAM);
          navigate(returnUrl || NAVIGATION_CONFIG.DEFAULT_REDIRECT);
        }

        const { data: user } = await supabase
          .from("profiles")
          .select("*")
          .eq("id", initialSession.user.id)
          .single();

        // Check for admin domain and user type
        // if (
        //   (window.location.hostname === ADMIN_DOMAIN &&
        //     user?.user_type !== "admin") ||
        //   (window.location.hostname !== ADMIN_DOMAIN &&
        //     user?.user_type === "admin")
        // ) {
        //   await supabase.auth.signOut();
        //   setSession(null);
        //   setUserProfile(null);
        //   localStorage.clear();
        //   navigate(ROUTE_PATHS.AUTH, { replace: true });
        //   toast({
        //     variant: "destructive",
        //     title: "Geen toegang",
        //     description: "Je hebt geen toegang tot het admin portaal.",
        //   });
        //   return;
        // }

        setUserProfile(user as unknown as UserProfile);
      }
    } catch (error) {
      console.error("Unexpected error during session initialization:", error);
      handleSessionError();
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Initialize session
    initializeSession();
  }, [currentIsPublicRoute, navigate, toast]);

  useEffect(() => {
    // Set up auth state change listener
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, currentSession) => {
      if (event === "SIGNED_OUT") {
        setSession(null);
        localStorage.clear();
        navigate(ROUTE_PATHS.AUTH);
      } else if (event === "SIGNED_IN" || event === "TOKEN_REFRESHED") {
        if (currentSession) {
          setSession(currentSession);

          if (
            RESTRICTED_PUBLIC_ROUTES.includes(location.pathname as any) &&
            currentIsPublicRoute
          ) {
            navigate(NAVIGATION_CONFIG.DEFAULT_REDIRECT);
          }
        }
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [currentIsPublicRoute, location.pathname, navigate]);

  useEffect(() => {
    // Send notification to vakman when new job was created
    const channel = supabase
      .channel("job_responses-changes")
      .on(
        "postgres_changes",
        { event: "INSERT", schema: "public", table: "job_responses" },
        async (payload) => {
          if (!session) {
            return;
          }

          const { data: profile } = await supabase
            .from("profiles")
            .select("user_type")
            .eq("id", session.user.id)
            .single();

          if (profile.user_type === "klusaanvrager") {
            const { data: job } = await supabase
              .from("jobs")
              .select("user_id")
              .eq("id", payload.new.job_id)
              .single();

            if (job.user_id === session.user.id) {
              toast(TOAST_MESSAGES.NEW_RESPONSE);
            }
          }
        }
      )
      .subscribe();

    return () => {
      channel.unsubscribe();
    };
  }, [session, toast]);

  const emptyUserProfile = () => {
    setUserProfile(null);
  };

  if (loading) {
    return (
      <div className="h-screen w-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <SessionContext.Provider
      value={{
        session,
        userProfile,
        emptyUserProfile,
        initializeSession,
      }}
    >
      {children(session)}
    </SessionContext.Provider>
  );
};
